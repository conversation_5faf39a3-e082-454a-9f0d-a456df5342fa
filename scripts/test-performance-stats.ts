#!/usr/bin/env tsx

// Test script to debug performance stats
// Run with: npx tsx scripts/test-performance-stats.ts

import { getManagerPerformanceStats, getEmployees, getPeriods } from '../lib/data'

async function testPerformanceStats() {
  console.log('🧪 [TEST] Starting performance stats test...')
  
  try {
    // Test basic data access
    console.log('\n📊 [TEST] Testing basic data access...')
    
    const employees = await getEmployees()
    console.log('👥 [TEST] Employees found:', employees.length)
    employees.forEach(emp => {
      console.log(`  - ${emp.fullName} (Manager: ${emp.managerId || 'None'})`)
    })
    
    const periods = await getPeriods()
    console.log('\n📅 [TEST] Periods found:', periods.length)
    periods.forEach(period => {
      console.log(`  - ${period.id}: ${period.periodStart} to ${period.periodEnd} (Closed: ${period.closed})`)
    })
    
    // Test performance stats
    console.log('\n📈 [TEST] Testing performance stats...')
    const stats = await getManagerPerformanceStats()
    
    console.log('✅ [TEST] Performance stats result:', {
      total: stats.total,
      belowExpectations: stats.belowExpectations,
      meetsExpectations: stats.meetsExpectations,
      exceedsExpectations: stats.exceedsExpectations,
      notStarted: stats.notStarted,
      submittedCount: stats.submittedCount,
      draftCount: stats.draftCount
    })
    
    if (stats.total === 0) {
      console.log('🚨 [TEST] No employees found! This explains why Team Performance Distribution shows "No data available"')
      console.log('💡 [TEST] Possible issues:')
      console.log('  1. No employees in database')
      console.log('  2. No employees assigned to current user as manager')
      console.log('  3. Authentication issue - current user not found')
      console.log('  4. No active appraisal period')
    } else {
      console.log('✅ [TEST] Found employees! Performance distribution should work.')
    }
    
  } catch (error) {
    console.error('❌ [TEST] Error during test:', error)
  }
}

// Run the test
testPerformanceStats().then(() => {
  console.log('\n🏁 [TEST] Test completed')
  process.exit(0)
}).catch(error => {
  console.error('💥 [TEST] Test failed:', error)
  process.exit(1)
})
